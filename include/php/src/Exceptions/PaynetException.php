<?php

namespace Exceptions;

/**
 * Базовое исключение для модуля Paynet
 */
class PaynetException extends \Exception
{
    protected $context = [];
    
    public function __construct(string $message = "", int $code = 0, \Throwable $previous = null, array $context = [])
    {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }
    
    /**
     * Получить контекст ошибки
     */
    public function getContext(): array
    {
        return $this->context;
    }
    
    /**
     * Установить контекст ошибки
     */
    public function setContext(array $context): self
    {
        $this->context = $context;
        return $this;
    }
    
    /**
     * Получить полную информацию об ошибке
     */
    public function getFullInfo(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'context' => $this->context,
            'trace' => $this->getTraceAsString()
        ];
    }
}
