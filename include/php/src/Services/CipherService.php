<?php

namespace Services;

/**
 * Сервис шифрования для модуля Paynet
 */
class CipherService
{
    private $cipher;
    
    public function __construct()
    {
        // Используем существующий класс XORNumberCipherSimple
        $this->cipher = new \XORNumberCipherSimple();
    }
    
    /**
     * Зашифровать значение
     */
    public function encrypt($value): string
    {
        return $this->cipher->encrypt($value);
    }
    
    /**
     * Расшифровать значение
     */
    public function decrypt(string $encryptedValue)
    {
        return $this->cipher->decrypt($encryptedValue);
    }
}
