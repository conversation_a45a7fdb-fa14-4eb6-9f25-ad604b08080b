<?php

namespace Services;

/**
 * Сервис логирования для модуля Paynet
 */
class LoggerService
{
    private $logFile;
    private $moduleName;
    
    public function __construct(string $moduleName = 'pmpaynet')
    {
        $this->moduleName = $moduleName;
        $this->initLogFile();
    }
    
    /**
     * Инициализация файла логов
     */
    private function initLogFile(): void
    {
        $logDir = '/usr/local/mgr5/var';
        if (is_dir($logDir) && is_writable($logDir)) {
            $this->logFile = fopen($logDir . '/' . $this->moduleName . '.log', 'a');
        } else {
            $this->logFile = false;
        }
    }
    
    /**
     * Записать отладочное сообщение
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log('DEBUG', $message, $context);
    }
    
    /**
     * Записать информационное сообщение
     */
    public function info(string $message, array $context = []): void
    {
        $this->log('INFO', $message, $context);
    }
    
    /**
     * Записать предупреждение
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log('WARNING', $message, $context);
    }
    
    /**
     * Записать ошибку
     */
    public function error(string $message, array $context = []): void
    {
        $this->log('ERROR', $message, $context);
    }
    
    /**
     * Записать критическую ошибку
     */
    public function critical(string $message, array $context = []): void
    {
        $this->log('CRITICAL', $message, $context);
    }
    
    /**
     * Основной метод логирования
     */
    private function log(string $level, string $message, array $context = []): void
    {
        $contextStr = !empty($context) ? ' Context: ' . json_encode($context) : '';
        $logMessage = date("M j H:i:s") . " [" . getmypid() . "] " . $this->moduleName . " {$level} " . $message . $contextStr . "\n";
        
        if ($this->logFile) {
            fwrite($this->logFile, $logMessage);
        } else {
            error_log($logMessage);
        }
    }
    
    /**
     * Закрыть файл логов
     */
    public function __destruct()
    {
        if ($this->logFile && is_resource($this->logFile)) {
            fclose($this->logFile);
        }
    }
}
