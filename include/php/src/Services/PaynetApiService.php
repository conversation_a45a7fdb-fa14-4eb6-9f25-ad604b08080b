<?php

namespace Services;

use Config\PaynetConfig;
use Exceptions\PaymentException;

/**
 * Сервис для работы с API Paynet
 */
class PaynetApiService
{
    private PaynetConfig $config;
    private LoggerService $logger;
    private \PaynetEcomAPI $api;
    
    public function __construct(PaynetConfig $config, LoggerService $logger)
    {
        $this->config = $config;
        $this->logger = $logger;
        $this->initApi();
    }
    
    /**
     * Инициализация API
     */
    private function initApi(): void
    {
        $this->api = new \PaynetEcomAPI(
            $this->config->getMerchantCode(),
            $this->config->getMerchantSecretKey(),
            $this->config->getMerchantUser(),
            $this->config->getMerchantUserPassword(),
            $this->config->isTestMode()
        );
    }
    
    /**
     * Создать форму оплаты
     */
    public function createPaymentForm(\PaynetRequest $request, string $currency): \PaynetResult
    {
        try {
            $this->logger->debug('Creating payment form', [
                'external_id' => $request->ExternalID,
                'amount' => $request->Amount,
                'currency' => $currency,
                'test_mode' => $this->config->isTestMode()
            ]);
            
            $result = $this->api->FormCreate($request, $currency);
            
            if ($result->IsOk()) {
                $this->logger->info('Payment form created successfully', [
                    'external_id' => $request->ExternalID
                ]);
            } else {
                $this->logger->error('Payment form creation failed', [
                    'external_id' => $request->ExternalID,
                    'error_code' => $result->Code,
                    'error_message' => $result->Message
                ]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error('Exception during payment form creation', [
                'external_id' => $request->ExternalID,
                'exception' => $e->getMessage()
            ]);
            
            throw new PaymentException(
                'Failed to create payment form: ' . $e->getMessage(),
                2002,
                $e,
                ['external_id' => $request->ExternalID]
            );
        }
    }
    
    /**
     * Получить информацию о платеже
     */
    public function getPaymentInfo(string $externalId): \PaynetResult
    {
        try {
            $this->logger->debug('Getting payment info', ['external_id' => $externalId]);
            
            $result = $this->api->PaymentGet($externalId);
            
            if ($result->IsOk()) {
                $this->logger->debug('Payment info retrieved successfully', [
                    'external_id' => $externalId
                ]);
            } else {
                $this->logger->warning('Failed to get payment info', [
                    'external_id' => $externalId,
                    'error_code' => $result->Code,
                    'error_message' => $result->Message
                ]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error('Exception during payment info retrieval', [
                'external_id' => $externalId,
                'exception' => $e->getMessage()
            ]);
            
            throw new PaymentException(
                'Failed to get payment info: ' . $e->getMessage(),
                2003,
                $e,
                ['external_id' => $externalId]
            );
        }
    }
}
