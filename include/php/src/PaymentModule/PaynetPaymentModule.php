<?php

namespace PaymentModule;

use Config\PaynetConfig;
use Services\LoggerService;
use Services\PaynetApiService;
use Services\CipherService;
use Handlers\PaymentFormHandler;
use Handlers\ResultHandler;
use Handlers\SuccessHandler;
use Handlers\CancelHandler;
use Exceptions\ConfigurationException;
use Exceptions\PaymentException;

/**
 * Главный класс модуля платежей Paynet
 */
class PaynetPaymentModule
{
    private LoggerService $logger;
    private PaynetConfig $config;
    private PaynetApiService $apiService;
    private CipherService $cipher;
    
    private PaymentFormHandler $formHandler;
    private ResultHandler $resultHandler;
    private SuccessHandler $successHandler;
    private CancelHandler $cancelHandler;
    
    public function __construct()
    {
        $this->logger = new LoggerService('pmpaynet');
        $this->cipher = new CipherService();
    }
    
    /**
     * Инициализация модуля с конфигурацией
     */
    public function initialize(array $paymethod): self
    {
        try {
            $this->config = new PaynetConfig($paymethod);
            $this->config->validate();
            
            $this->apiService = new PaynetApiService($this->config, $this->logger);
            
            $this->initializeHandlers();
            
            $this->logger->info('Paynet module initialized successfully');
            
        } catch (ConfigurationException $e) {
            $this->logger->error('Configuration error during initialization', $e->getContext());
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error('Unexpected error during initialization', ['error' => $e->getMessage()]);
            throw new PaymentException('Module initialization failed: ' . $e->getMessage(), 2000, $e);
        }
        
        return $this;
    }
    
    /**
     * Инициализация обработчиков
     */
    private function initializeHandlers(): void
    {
        $this->formHandler = new PaymentFormHandler($this->config, $this->logger, $this->apiService, $this->cipher);
        $this->resultHandler = new ResultHandler($this->config, $this->logger, $this->apiService);
        $this->successHandler = new SuccessHandler($this->logger, $this->cipher);
        $this->cancelHandler = new CancelHandler($this->logger, $this->cipher);
    }
    
    /**
     * Получить конфигурацию модуля для BillManager
     */
    public function getModuleConfig(): string
    {
        return '<?xml version="1.0" encoding="UTF-8"?>
<doc>
    <feature>
        <redirect>on</redirect>
        <notneedprofile>on</notneedprofile>
    </feature>
    <param>
        <payment_script>/mancgi/paynet_payment.php</payment_script>
        <result_script>/mancgi/paynet_result.php</result_script>
    </param>
</doc>';
    }
    
    /**
     * Создать форму оплаты
     */
    public function createPaymentForm(array $payment): string
    {
        $this->logger->info('Creating payment form', ['order_id' => $payment['id']['$']]);
        
        try {
            return $this->formHandler->createPaymentForm($payment);
        } catch (\Exception $e) {
            $this->logger->error('Error creating payment form', [
                'order_id' => $payment['id']['$'],
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Обработать колбэк от Paynet
     */
    public function handleCallback(array $paymentInfo): array
    {
        $this->logger->info('Handling Paynet callback');
        
        try {
            return $this->resultHandler->handleCallback($paymentInfo);
        } catch (\Exception $e) {
            $this->logger->error('Error handling callback', ['error' => $e->getMessage()]);
            return ['status' => 'error', 'message' => 'Internal error'];
        }
    }
    
    /**
     * Обработать успешный платеж
     */
    public function handleSuccess(array $input): string
    {
        $this->logger->info('Handling success callback');
        
        try {
            return $this->successHandler->handleSuccess($input);
        } catch (\Exception $e) {
            $this->logger->error('Error handling success', ['error' => $e->getMessage()]);
            return $this->generateErrorResponse('Unable to process success callback');
        }
    }
    
    /**
     * Обработать отмену платежа
     */
    public function handleCancel(array $input): string
    {
        $this->logger->info('Handling cancel callback');
        
        try {
            return $this->cancelHandler->handleCancel($input);
        } catch (\Exception $e) {
            $this->logger->error('Error handling cancel', ['error' => $e->getMessage()]);
            return $this->generateErrorResponse('Unable to process cancel callback');
        }
    }
    
    /**
     * Верифицировать статус платежа
     */
    public function verifyPaymentStatus(string $orderId): string
    {
        $this->logger->info('Verifying payment status', ['order_id' => $orderId]);
        
        try {
            return $this->resultHandler->verifyPaymentStatus($orderId);
        } catch (\Exception $e) {
            $this->logger->error('Error verifying payment status', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return 'error';
        }
    }
    
    /**
     * Сгенерировать ответ об ошибке
     */
    private function generateErrorResponse(string $message): string
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <title>Error</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>Error</h1>
    <p>' . htmlspecialchars($message) . '</p>
</body>
</html>';
    }
    
    /**
     * Получить логгер
     */
    public function getLogger(): LoggerService
    {
        return $this->logger;
    }
    
    /**
     * Получить конфигурацию
     */
    public function getConfig(): PaynetConfig
    {
        return $this->config;
    }
}
