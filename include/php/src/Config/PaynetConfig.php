<?php

namespace Config;

use Exceptions\ConfigurationException;

/**
 * Конфигурация модуля Paynet
 */
class PaynetConfig
{
    private array $config = [];
    private array $requiredFields = [
        'merchant_code',
        'merchant_secret_key', 
        'merchant_user',
        'merchant_user_password'
    ];
    
    public function __construct(array $paymethod = [])
    {
        if (!empty($paymethod)) {
            $this->loadFromPaymethod($paymethod);
        }
    }
    
    /**
     * Загрузить конфигурацию из данных метода оплаты BillManager
     */
    public function loadFromPaymethod(array $paymethod): self
    {
        $this->config = [];
        
        foreach ($this->requiredFields as $field) {
            if (!isset($paymethod[$field]['$']) || empty($paymethod[$field]['$'])) {
                throw new ConfigurationException(
                    "Missing required field: {$field}",
                    1001,
                    null,
                    ['field' => $field, 'paymethod' => $paymethod]
                );
            }
            $this->config[$field] = $paymethod[$field]['$'];
        }
        
        // Опциональные поля
        $this->config['test_mode'] = isset($paymethod['test_mode']['$']) && $paymethod['test_mode']['$'] === 'on';
        
        return $this;
    }
    
    /**
     * Получить код мерчанта
     */
    public function getMerchantCode(): string
    {
        return $this->config['merchant_code'] ?? '';
    }
    
    /**
     * Получить секретный ключ мерчанта
     */
    public function getMerchantSecretKey(): string
    {
        return $this->config['merchant_secret_key'] ?? '';
    }
    
    /**
     * Получить пользователя мерчанта
     */
    public function getMerchantUser(): string
    {
        return $this->config['merchant_user'] ?? '';
    }
    
    /**
     * Получить пароль пользователя мерчанта
     */
    public function getMerchantUserPassword(): string
    {
        return $this->config['merchant_user_password'] ?? '';
    }
    
    /**
     * Проверить, включен ли тестовый режим
     */
    public function isTestMode(): bool
    {
        return $this->config['test_mode'] ?? false;
    }
    
    /**
     * Получить всю конфигурацию
     */
    public function getAll(): array
    {
        return $this->config;
    }
    
    /**
     * Валидировать конфигурацию
     */
    public function validate(): bool
    {
        foreach ($this->requiredFields as $field) {
            if (empty($this->config[$field])) {
                throw new ConfigurationException("Configuration field '{$field}' is required but empty");
            }
        }
        
        return true;
    }
}
