<?php

namespace Handlers;

use Services\LoggerService;
use Services\CipherService;

/**
 * Обработчик успешных платежей
 */
class SuccessHandler
{
    private LoggerService $logger;
    private CipherService $cipher;
    
    public function __construct(LoggerService $logger, CipherService $cipher)
    {
        $this->logger = $logger;
        $this->cipher = $cipher;
    }
    
    /**
     * Обработать успешный платеж
     */
    public function handleSuccess(array $input): string
    {
        $this->logger->info('Processing success callback');
        
        // Расшифровываем ID заказа
        if (empty($input['elid'])) {
            $this->logger->error('Missing elid parameter in success callback');
            return $this->generateErrorPage('Invalid request parameters');
        }
        
        try {
            $orderId = $this->cipher->decrypt($input['elid']);
            $this->logger->info('Success callback processed', ['order_id' => $orderId]);
            
            return $this->generateSuccessPage($orderId);
            
        } catch (\Exception $e) {
            $this->logger->error('Error processing success callback', [
                'error' => $e->getMessage(),
                'elid' => $input['elid']
            ]);
            return $this->generateErrorPage('Unable to process success callback');
        }
    }
    
    /**
     * Сгенерировать страницу успешной оплаты
     */
    private function generateSuccessPage(string $orderId): string
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <title>Payment Successful</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
            background-color: #f5f5f5;
        }
        .success-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success-icon {
            color: #28a745;
            font-size: 48px;
            margin-bottom: 20px;
        }
        h1 {
            color: #28a745;
            margin-bottom: 20px;
        }
        .order-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">✓</div>
        <h1>Payment Successful!</h1>
        <p>Your payment has been processed successfully.</p>
        <div class="order-info">
            <strong>Order ID:</strong> ' . htmlspecialchars($orderId) . '
        </div>
        <p>Thank you for your payment. You will receive a confirmation email shortly.</p>
        <a href="javascript:window.close();" class="btn">Close Window</a>
    </div>
    <script>
        // Auto-close window after 5 seconds
        setTimeout(function() {
            window.close();
        }, 5000);
    </script>
</body>
</html>';
    }
    
    /**
     * Сгенерировать страницу с ошибкой
     */
    private function generateErrorPage(string $errorMessage): string
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <title>Payment Error</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
            background-color: #f5f5f5;
        }
        .error-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .error-icon {
            color: #dc3545;
            font-size: 48px;
            margin-bottom: 20px;
        }
        h1 {
            color: #dc3545;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .btn:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">✗</div>
        <h1>Payment Error</h1>
        <p>' . htmlspecialchars($errorMessage) . '</p>
        <a href="javascript:window.close();" class="btn">Close Window</a>
    </div>
</body>
</html>';
    }
}
