<?php

namespace Handlers;

use Services\LoggerService;
use Services\CipherService;

/**
 * Обработчик отмененных платежей
 */
class CancelHandler
{
    private LoggerService $logger;
    private CipherService $cipher;
    
    public function __construct(LoggerService $logger, CipherService $cipher)
    {
        $this->logger = $logger;
        $this->cipher = $cipher;
    }
    
    /**
     * Обработать отмененный платеж
     */
    public function handleCancel(array $input): string
    {
        $this->logger->info('Processing cancel callback');
        
        // Расшифровываем ID заказа
        if (empty($input['elid'])) {
            $this->logger->error('Missing elid parameter in cancel callback');
            return $this->generateErrorPage('Invalid request parameters');
        }
        
        try {
            $orderId = $this->cipher->decrypt($input['elid']);
            $this->logger->info('Cancel callback processed', ['order_id' => $orderId]);
            
            // Обновляем статус платежа в BillManager
            $this->updatePaymentStatus($orderId);
            
            return $this->generateCancelPage($orderId);
            
        } catch (\Exception $e) {
            $this->logger->error('Error processing cancel callback', [
                'error' => $e->getMessage(),
                'elid' => $input['elid']
            ]);
            return $this->generateErrorPage('Unable to process cancel callback');
        }
    }
    
    /**
     * Обновить статус платежа в BillManager
     */
    private function updatePaymentStatus(string $orderId): void
    {
        try {
            \LocalQuery('payment.setinpay', [
                'elid' => $orderId,
                'info' => json_encode(['step' => 'payment_canceled', 'reason' => 'User canceled payment'])
            ]);
            
            $this->logger->info('Payment status updated to canceled', ['order_id' => $orderId]);
            
        } catch (\Exception $e) {
            $this->logger->error('Error updating payment status', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Сгенерировать страницу отмены платежа
     */
    private function generateCancelPage(string $orderId): string
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <title>Payment Canceled</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
            background-color: #f5f5f5;
        }
        .cancel-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .cancel-icon {
            color: #ffc107;
            font-size: 48px;
            margin-bottom: 20px;
        }
        h1 {
            color: #ffc107;
            margin-bottom: 20px;
        }
        .order-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .btn:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="cancel-container">
        <div class="cancel-icon">⚠</div>
        <h1>Payment Canceled</h1>
        <p>Your payment has been canceled.</p>
        <div class="order-info">
            <strong>Order ID:</strong> ' . htmlspecialchars($orderId) . '
        </div>
        <p>You can try to make the payment again or contact support if you need assistance.</p>
        <a href="javascript:window.close();" class="btn">Close Window</a>
    </div>
    <script>
        // Auto-close window after 5 seconds
        setTimeout(function() {
            window.close();
        }, 5000);
    </script>
</body>
</html>';
    }
    
    /**
     * Сгенерировать страницу с ошибкой
     */
    private function generateErrorPage(string $errorMessage): string
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <title>Error</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
            background-color: #f5f5f5;
        }
        .error-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .error-icon {
            color: #dc3545;
            font-size: 48px;
            margin-bottom: 20px;
        }
        h1 {
            color: #dc3545;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .btn:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">✗</div>
        <h1>Error</h1>
        <p>' . htmlspecialchars($errorMessage) . '</p>
        <a href="javascript:window.close();" class="btn">Close Window</a>
    </div>
</body>
</html>';
    }
}
