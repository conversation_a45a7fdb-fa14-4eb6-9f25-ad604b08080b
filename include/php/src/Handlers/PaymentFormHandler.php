<?php

namespace Handlers;

use Config\PaynetConfig;
use Services\LoggerService;
use Services\PaynetApiService;
use Services\CipherService;
use Exceptions\PaymentException;

/**
 * Обработчик форм оплаты
 */
class PaymentFormHandler
{
    private PaynetConfig $config;
    private LoggerService $logger;
    private PaynetApiService $apiService;
    private CipherService $cipher;
    
    public function __construct(PaynetConfig $config, LoggerService $logger, PaynetApiService $apiService, CipherService $cipher)
    {
        $this->config = $config;
        $this->logger = $logger;
        $this->apiService = $apiService;
        $this->cipher = $cipher;
    }
    
    /**
     * Создать форму оплаты
     */
    public function createPaymentForm(array $payment): string
    {
        $orderId = $payment['id']['$'];
        $amount = intval(floatval($payment['paymethodamount']['$']) * 100); // Convert to coins
        
        $this->logger->info('Creating payment form', [
            'order_id' => $orderId,
            'amount' => $amount
        ]);
        
        // Создаем запрос к Paynet
        $request = $this->buildPaynetRequest($payment);
        
        // Получаем валюту
        $currencyName = $payment['currency'][1]['iso']['$'];
        
        try {
            // Создаем форму через API
            $formResult = $this->apiService->createPaymentForm($request, $currencyName);
            
            if ($formResult->IsOk()) {
                // Обновляем статус платежа в BillManager
                \LocalQuery('payment.setnopay', [
                    'elid' => $orderId, 
                    'info' => json_encode(['step' => 'payment_created'])
                ]);
                
                $this->logger->info('Payment form created successfully', ['order_id' => $orderId]);
                return $formResult->Data;
            } else {
                throw new PaymentException(
                    'Payment form creation failed: ' . $formResult->Message,
                    2001,
                    null,
                    ['order_id' => $orderId, 'error_code' => $formResult->Code]
                );
            }
            
        } catch (\Exception $e) {
            $this->logger->error('Payment form creation error', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            
            return $this->generateErrorPage($e->getMessage());
        }
    }
    
    /**
     * Построить запрос к Paynet
     */
    private function buildPaynetRequest(array $payment): \PaynetRequest
    {
        $orderId = $payment['id']['$'];
        $amount = intval(floatval($payment['paymethodamount']['$']) * 100);
        $desc = $payment['project']['name']['$'] . ' #' . $orderId;
        
        // Определяем язык
        $lang = $this->getLanguage();
        
        $request = new \PaynetRequest();
        $request->ExternalID = $orderId;
        $request->LinkSuccess = $this->buildCallbackUrl('paynet_success.php', $orderId);
        $request->LinkCancel = $this->buildCallbackUrl('paynet_cancel.php', $orderId);
        $request->Lang = $lang;
        $request->Amount = $amount;
        $request->Currency = $payment['currency'][1]['code']['$'];
        
        // Настройка продуктов
        $request->Products = [
            [
                'LineNo' => '1',
                'Code' => 'payment_' . $orderId,
                'Barcode' => $orderId,
                'Name' => $desc,
                'Descrption' => $desc,
                'Quantity' => 100, // 100 = 1.00
                'UnitPrice' => $amount
            ]
        ];
        
        // Настройка сервиса
        $request->Service = [
            'Name' => 'BillManager Payment',
            'Description' => $desc,
            'Amount' => $amount,
            'Products' => $request->Products
        ];
        
        // Настройка клиента
        $request->Customer = [
            'Code' => $payment['user_id']['$'] ?? 'customer_' . $orderId,
            'Address' => $_SERVER['HTTP_HOST'] ?? 'localhost',
            'Name' => $payment['userrealname']['$'] ?? 'Customer',
            'Email' => $payment['useremail']['$'] ?? ''
        ];
        
        return $request;
    }
    
    /**
     * Получить язык интерфейса
     */
    private function getLanguage(): string
    {
        $lang = \_get_locale_lang("billmgrlang5");
        if (is_null($lang)) {
            $lang = \_get_locale_lang("billmgrlang6");
        }
        return (in_array($lang, ['ru', 'en', 'ro'])) ? $lang : 'ru';
    }
    
    /**
     * Построить URL для колбэка
     */
    private function buildCallbackUrl(string $script, string $orderId): string
    {
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $encryptedId = $this->cipher->encrypt($orderId);
        return "https://{$host}/mancgi/{$script}?elid={$encryptedId}";
    }
    
    /**
     * Сгенерировать страницу с ошибкой
     */
    private function generateErrorPage(string $errorMessage): string
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <title>Payment Error</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>Payment Error</h1>
    <p>Unable to process payment. Please try again later.</p>
    <p>Error: ' . htmlspecialchars($errorMessage) . '</p>
</body>
</html>';
    }
}
