<?php

namespace Handlers;

use Config\PaynetConfig;
use Services\LoggerService;
use Services\PaynetApiService;
use Exceptions\PaymentException;

/**
 * Обработчик результатов платежей (колбэков от Paynet)
 */
class ResultHandler
{
    private PaynetConfig $config;
    private LoggerService $logger;
    private PaynetApiService $apiService;
    
    public function __construct(PaynetConfig $config, LoggerService $logger, PaynetApiService $apiService)
    {
        $this->config = $config;
        $this->logger = $logger;
        $this->apiService = $apiService;
    }
    
    /**
     * Обработать колбэк от Paynet
     */
    public function handleCallback(array $paymentInfo): array
    {
        $this->logger->info('Processing Paynet callback', [
            'event_type' => $paymentInfo['EventType'] ?? 'unknown'
        ]);
        
        // Проверяем тип события
        if (!isset($paymentInfo['EventType']) || $paymentInfo['EventType'] !== 'PAID') {
            $this->logger->warning('Unsupported event type', [
                'event_type' => $paymentInfo['EventType'] ?? 'unknown'
            ]);
            return ['status' => 'error', 'message' => 'Event type not supported'];
        }
        
        // Получаем ID заказа
        if (!isset($paymentInfo['Payment']['ExternalId'])) {
            $this->logger->error('ExternalId missing in callback');
            return ['status' => 'error', 'message' => 'ExternalId missing'];
        }
        
        $orderId = $paymentInfo['Payment']['ExternalId'];
        
        try {
            return $this->processPaidEvent($orderId, $paymentInfo);
        } catch (\Exception $e) {
            $this->logger->error('Error processing callback', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return ['status' => 'error', 'message' => 'Internal error'];
        }
    }
    
    /**
     * Обработать событие PAID
     */
    private function processPaidEvent(string $orderId, array $paymentInfo): array
    {
        $this->logger->info('Processing PAID event', ['order_id' => $orderId]);
        
        // Получаем информацию о платеже из BillManager
        $paymentData = $this->getPaymentFromBillManager($orderId);
        if (!$paymentData) {
            return ['status' => 'error', 'message' => 'Order not found'];
        }
        
        // Отмечаем платеж как оплаченный
        $this->markPaymentAsPaid($orderId, $paymentInfo);
        
        $this->logger->info('Payment marked as paid successfully', ['order_id' => $orderId]);
        
        return ['status' => 'ok'];
    }
    
    /**
     * Получить данные платежа из BillManager
     */
    private function getPaymentFromBillManager(string $orderId): ?array
    {
        try {
            $info = \LocalQuery('payment.info', ['elid' => $orderId]);
            
            if (empty($info['payment'][0])) {
                $this->logger->warning('Order not found in BillManager', ['order_id' => $orderId]);
                return null;
            }
            
            return $info['payment'][0];
            
        } catch (\Exception $e) {
            $this->logger->error('Error getting payment info from BillManager', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * Отметить платеж как оплаченный в BillManager
     */
    private function markPaymentAsPaid(string $orderId, array $paymentInfo): void
    {
        try {
            \LocalQuery('payment.setpaid', [
                'elid' => $orderId,
                'info' => 'Paynet payment completed. Transaction data: ' . json_encode($paymentInfo)
            ]);
            
            $this->logger->info('Payment marked as paid in BillManager', ['order_id' => $orderId]);
            
        } catch (\Exception $e) {
            $this->logger->error('Error marking payment as paid', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            throw new PaymentException(
                'Failed to mark payment as paid: ' . $e->getMessage(),
                2004,
                $e,
                ['order_id' => $orderId]
            );
        }
    }
    
    /**
     * Верифицировать статус платежа через API Paynet
     */
    public function verifyPaymentStatus(string $orderId): string
    {
        $this->logger->debug('Verifying payment status', ['order_id' => $orderId]);
        
        try {
            $result = $this->apiService->getPaymentInfo($orderId);
            
            if (!$result->IsOk()) {
                $this->logger->warning('Failed to verify payment status', [
                    'order_id' => $orderId,
                    'error' => $result->Message
                ]);
                return 'verification_failed';
            }
            
            // Проверяем статус платежа
            if (!isset($result->Data[0]['Status'])) {
                $this->logger->warning('Payment status not found in response', ['order_id' => $orderId]);
                return 'not_completed';
            }
            
            $status = $result->Data[0]['Status'];
            
            return match ($status) {
                1 => 'REGISTERED',
                2 => 'CUSTOMER_VERIFIED', 
                3 => 'INITIALIZED_TO_BE_PAID',
                4 => 'PAID',
                default => 'unknown'
            };
            
        } catch (\Exception $e) {
            $this->logger->error('Error verifying payment status', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return 'error';
        }
    }
}
