<?php
/**
 * Автозагрузчик классов для Paynet модуля
 */

spl_autoload_register(function ($className) {
    // Базовая директория для пространства имен проекта
    $baseDir = __DIR__ . '/src/';
    
    // Преобразуем пространство имен в путь к файлу
    $file = $baseDir . str_replace('\\', '/', $className) . '.php';
    
    // Если файл существует, подключаем его
    if (file_exists($file)) {
        require_once $file;
        return true;
    }
    
    return false;
});

// Подключаем legacy файлы для обратной совместимости
$legacyFiles = [
    'PaynetAPI.php',
    'PaymentStatusEnum.php',
    'PaymentStatusVerifier.php',
    'XORNumberCipherSimple.php',
    'bill_func_paynet.php'
];

foreach ($legacyFiles as $file) {
    $filePath = __DIR__ . '/' . $file;
    if (file_exists($filePath)) {
        require_once $filePath;
    }
}
