
#!/bin/bash

# Установочный скрипт для модуля Paynet для BILLmanager 6

echo "Установка модуля Paynet для BILLmanager 6..."

# Проверяем, что мы запущены от root
if [ "$EUID" -ne 0 ]; then
    echo "Пожалуйста, запустите скрипт от имени root"
    exit 1
fi

# Проверяем наличие BILLmanager
if [ ! -d "/usr/local/mgr5" ]; then
    echo "BILLmanager не найден в /usr/local/mgr5"
    exit 1
fi

# Создаем временный скрипт для тестирования enum
cat > /tmp/test_php_enum.php << 'EOF'
<?php
enum TestEnum: string { 
    case TEST = 'test'; 
}
echo "PHP_ENUM_SUPPORTED";
EOF

# Функция для проверки версии PHP
check_php_version() {
    local php_cmd="$1"
    if command -v "$php_cmd" >/dev/null 2>&1; then
        local PHP_VERSION=$("$php_cmd" -r "echo PHP_VERSION;" 2>/dev/null)
        local PHP_MAJOR=$(echo $PHP_VERSION | cut -d. -f1)
        local PHP_MINOR=$(echo $PHP_VERSION | cut -d. -f2)
        
        if [ "$PHP_MAJOR" -gt 8 ] || ([ "$PHP_MAJOR" -eq 8 ] && [ "$PHP_MINOR" -ge 1 ]); then
            echo "✓ $php_cmd: версия $PHP_VERSION подходит"
            return 0
        else
            echo "⚠ $php_cmd: версия $PHP_VERSION не подходит (требуется 8.1+)"
            return 1
        fi
    else
        echo "⚠ $php_cmd не найден"
        return 2
    fi
}

# Функция для проверки поддержки enum в PHP
test_php_enum() {
    local php_cmd="$1"
    if command -v "$php_cmd" >/dev/null 2>&1; then
        if "$php_cmd" /tmp/test_php_enum.php 2>/dev/null | grep -q "PHP_ENUM_SUPPORTED"; then
            echo "✓ $php_cmd: поддерживает enum"
            return 0
        else
            echo "⚠ $php_cmd: не поддерживает enum"
            return 1
        fi
    else
        echo "⚠ $php_cmd не найден"
        return 2
    fi
}

# Определяем систему управления пакетами
detect_package_manager() {
    if command -v dnf >/dev/null 2>&1; then
        echo "dnf"
    elif command -v yum >/dev/null 2>&1; then
        echo "yum"
    elif command -v apt-get >/dev/null 2>&1; then
        echo "apt"
    else
        echo "unknown"
    fi
}

# Получаем систему управления пакетами
PKG_MANAGER=$(detect_package_manager)
echo "Обнаружен менеджер пакетов: $PKG_MANAGER"

# Определяем операционную систему
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$ID
        VERSION_ID=$VERSION_ID
        echo "Обнаружена ОС: $OS $VERSION_ID"
    elif [ -f /etc/redhat-release ]; then
        OS="rhel"
        VERSION_ID=$(rpm -E %{rhel})
        echo "Обнаружена ОС: RHEL/CentOS $VERSION_ID"
    else
        OS="unknown"
        VERSION_ID="unknown"
        echo "Не удалось определить ОС"
    fi
}

# Получаем информацию о ОС
detect_os

# Основной блок установки и настройки PHP 8.1
echo "Начинаем установку PHP 8.1..."

case $PKG_MANAGER in
    "dnf"|"yum")
        echo "Установка PHP 8.1 на RHEL/CentOS/AlmaLinux..."
        
        # Устанавливаем EPEL и Remi репозитории
        echo "Добавление необходимых репозиториев..."
        $PKG_MANAGER install -y epel-release
        
        # Устанавливаем Remi репозиторий в зависимости от версии
        if [[ "$OS" == "rhel" || "$OS" == "centos" || "$OS" == "almalinux" || "$OS" == "rocky" ]]; then
            case $VERSION_ID in
                "7")
                    $PKG_MANAGER install -y https://rpms.remirepo.net/enterprise/remi-release-7.rpm
                    ;;
                "8")
                    $PKG_MANAGER install -y https://rpms.remirepo.net/enterprise/remi-release-8.rpm
                    ;;
                "9")
                    $PKG_MANAGER install -y https://rpms.remirepo.net/enterprise/remi-release-9.rpm
                    ;;
                *)
                    echo "Предупреждение: Неизвестная версия RHEL-подобной системы: $VERSION_ID"
                    echo "Попытка установки универсальным способом..."
                    if [ "$PKG_MANAGER" == "dnf" ]; then
                        $PKG_MANAGER install -y https://rpms.remirepo.net/enterprise/remi-release-$(rpm -E %{rhel}).rpm
                    else
                        $PKG_MANAGER install -y https://rpms.remirepo.net/enterprise/remi-release-7.rpm
                    fi
                    ;;
            esac
        fi
        
        # Сбрасываем все модули PHP (если есть) и включаем PHP 8.1
        if [ "$PKG_MANAGER" == "dnf" ]; then
            dnf module reset php -y 2>/dev/null || true
            dnf module enable php:remi-8.1 -y 2>/dev/null || true
        fi
        
        # Устанавливаем все необходимые пакеты PHP 8.1
        echo "Установка пакетов PHP 8.1..."
        # Пробуем различные варианты имен пакетов в зависимости от системы
        if $PKG_MANAGER list php81-php &>/dev/null; then
            # Remi-style packages for RHEL/CentOS 7
            $PKG_MANAGER install -y php81-php php81-php-cli php81-php-common php81-php-xml php81-php-curl php81-php-json php81-php-mbstring
            # Создаем симлинки
            if [ -f /opt/remi/php81/root/usr/bin/php ]; then
                ln -sf /opt/remi/php81/root/usr/bin/php /usr/bin/php81
                ln -sf /usr/bin/php81 /usr/bin/php
            fi
        elif $PKG_MANAGER list php:8.1 &>/dev/null || $PKG_MANAGER module list php:remi-8.1 &>/dev/null; then
            # AppStream or module-based install for RHEL/CentOS 8+
            $PKG_MANAGER install -y php php-cli php-common php-xml php-curl php-json php-mbstring
        else
            # Fallback to standard package names
            $PKG_MANAGER install -y php php-cli php-common php-xml php-curl php-json php-mbstring
        fi
        
        # Проверяем различные варианты расположения PHP 8.1
        for php_path in /usr/bin/php /usr/bin/php81 /opt/remi/php81/root/usr/bin/php; do
            if [ -f "$php_path" ]; then
                echo "Найден PHP: $php_path"
                if ! check_php_version "$php_path" || ! test_php_enum "$php_path"; then
                    echo "⚠ PHP в $php_path не соответствует требованиям"
                else
                    echo "✓ PHP в $php_path соответствует требованиям"
                    # Создаем симлинк, если это не стандартный путь
                    if [ "$php_path" != "/usr/bin/php" ]; then
                        echo "Создание симлинка: $php_path -> /usr/bin/php"
                        ln -sf "$php_path" /usr/bin/php
                    fi
                    break
                fi
            fi
        done
        
        # Дополнительно для RHEL/CentOS 7: настройка через scl если установлен
        if [ "$VERSION_ID" == "7" ] && command -v scl >/dev/null 2>&1; then
            echo "Настройка PHP 8.1 через SCL..."
            $PKG_MANAGER install -y centos-release-scl
            $PKG_MANAGER install -y rh-php81 rh-php81-php-cli
            
            # Создаем скрипт-обертку для PHP 8.1
            cat > /usr/bin/php81 << 'EOF'
#!/bin/bash
source scl_source enable rh-php81
exec php "$@"
EOF
            chmod +x /usr/bin/php81
            ln -sf /usr/bin/php81 /usr/bin/php
        fi
        ;;
        
    "apt")
        echo "Установка PHP 8.1 на Debian/Ubuntu..."
        
        # Обновляем список пакетов
        apt-get update
        
        # Устанавливаем необходимые пакеты для PPA
        apt-get install -y software-properties-common apt-transport-https ca-certificates curl
        
        # Добавляем репозиторий Ondřej Surý для PHP
        if [[ "$OS" == "ubuntu" ]]; then
            echo "Добавление PPA для Ubuntu..."
            add-apt-repository -y ppa:ondrej/php
        elif [[ "$OS" == "debian" ]]; then
            echo "Добавление репозитория для Debian..."
            curl -sSLo /etc/apt/trusted.gpg.d/php.gpg https://packages.sury.org/php/apt.gpg
            echo "deb https://packages.sury.org/php/ $(lsb_release -sc) main" > /etc/apt/sources.list.d/php.list
        else
            echo "Попытка добавления репозитория для неизвестного дистрибутива на базе Debian..."
            add-apt-repository -y ppa:ondrej/php || {
                curl -sSLo /etc/apt/trusted.gpg.d/php.gpg https://packages.sury.org/php/apt.gpg
                echo "deb https://packages.sury.org/php/ $(lsb_release -sc) main" > /etc/apt/sources.list.d/php.list
            }
        fi
        
        # Обновляем список пакетов после добавления репозитория
        apt-get update
        
        # Устанавливаем PHP 8.1 и необходимые расширения
        echo "Установка пакетов PHP 8.1..."
        apt-get install -y php8.1 php8.1-cli php8.1-common php8.1-xml php8.1-curl php8.1-json php8.1-mbstring
        
        # Устанавливаем PHP 8.1 как версию по умолчанию
        echo "Настройка PHP 8.1 как версии по умолчанию..."
        update-alternatives --set php /usr/bin/php8.1 2>/dev/null || {
            echo "Не удалось использовать update-alternatives, создаем симлинк вручную..."
            if [ -f /usr/bin/php8.1 ]; then
                ln -sf /usr/bin/php8.1 /usr/bin/php
            fi
        }
        ;;
        
    *)
        echo "❌ Неподдерживаемый менеджер пакетов"
        echo "Поддерживаются: dnf, yum (RHEL/CentOS/AlmaLinux), apt (Ubuntu/Debian)"
        echo "Пожалуйста, установите PHP 8.1+ вручную и запустите скрипт снова"
        exit 1
        ;;
esac

# Проверка установки PHP 8.1
echo ""
echo "Проверка установки PHP 8.1..."

# Ищем все возможные пути к PHP 8.1
PHP_PATHS=(
    "/usr/bin/php"
    "/usr/bin/php81"
    "/usr/bin/php8.1"
    "/opt/remi/php81/root/usr/bin/php"
    "/opt/rh/rh-php81/root/usr/bin/php"
)

# Перебираем все пути и проверяем версию PHP
PHP_FOUND=0
PHP_VALID=0
PHP_CMD=""

for php_path in "${PHP_PATHS[@]}"; do
    if [ -f "$php_path" ] && [ -x "$php_path" ]; then
        PHP_FOUND=1
        echo "Найден PHP: $php_path, версия: $($php_path -r 'echo PHP_VERSION;' 2>/dev/null)"
        
        if check_php_version "$php_path" && test_php_enum "$php_path"; then
            PHP_VALID=1
            PHP_CMD="$php_path"
            echo "✅ PHP $php_path соответствует требованиям"
            
            # Если это не стандартный путь, создаем симлинк
            if [ "$php_path" != "/usr/bin/php" ]; then
                echo "Создание симлинка: $php_path -> /usr/bin/php"
                ln -sf "$php_path" /usr/bin/php
            fi
            
            break
        fi
    fi
done

# Если PHP не найден или не валиден, выводим ошибку
if [ "$PHP_FOUND" -eq 0 ]; then
    echo "❌ PHP не найден в системе"
    exit 1
elif [ "$PHP_VALID" -eq 0 ]; then
    echo "❌ Не найдена подходящая версия PHP 8.1+"
    echo "Установка не удалась. Пожалуйста, установите PHP 8.1+ вручную."
    exit 1
fi

# Финальная проверка PHP
echo ""
echo "Финальная проверка PHP..."
php -v
echo "PHP расположен в: $(which php)"
echo "Версия PHP: $(php -r 'echo PHP_VERSION;')"

# Проверяем поддержку enum
if php /tmp/test_php_enum.php 2>/dev/null | grep -q "PHP_ENUM_SUPPORTED"; then
    echo "✅ PHP поддерживает enum"
else
    echo "❌ PHP не поддерживает enum, требуется PHP 8.1+"
    exit 1
fi

echo "✅ PHP 8.1+ успешно установлен и настроен"

# Очистка временных файлов
rm -f /tmp/test_php_enum.php

# Создаем необходимые директории
mkdir -p /usr/local/mgr5/var

# Копируем файлы
echo "Копирование файлов..."

# Копируем CGI файлы
cp -r cgi/* /usr/local/mgr5/cgi/
chmod 755 /usr/local/mgr5/cgi/*.php

# XML конфигурация
cp etc/xml/*.xml /usr/local/mgr5/etc/xml/

# Создаем директорию для CGI файлов
mkdir -p /usr/local/mgr5/include/
cp -r include/* /usr/local/mgr5/include/
chmod 755 -R /usr/local/mgr5/include/

cp -r cgi/* /usr/local/mgr5/cgi/

cp -r paymethods/* /usr/local/mgr5/paymethods/

chmod 755 /usr/local/mgr5/paymethods/pmpaynet.php
echo "Файлы скопированы."

# Проверяем установку
echo "Проверка установленных файлов..."

if [ -f "/usr/local/mgr5/etc/xml/billmgr_mod_pmpaynet.xml" ]; then
    echo "✓ XML конфигурация установлена: /usr/local/mgr5/etc/xml/billmgr_mod_pmpaynet.xml"
else
    echo "❌ XML конфигурация не найдена: /usr/local/mgr5/etc/xml/billmgr_mod_pmpaynet.xml"
fi

# Создаем шебанг для PHP 8.1 в начале всех PHP-файлов
echo "Настройка шебангов в PHP-файлах..."
for php_file in $(find /usr/local/mgr5/paymethods -name "*.php") $(find /usr/local/mgr5/cgi -name "*.php") $(find /usr/local/mgr5/include -name "*.php"); do
    if [ -f "$php_file" ]; then
        # Проверяем, есть ли уже шебанг
        if head -n 1 "$php_file" | grep -q "^#!"; then
            # Заменяем существующий шебанг
            sed -i "1s|^#!.*|#!$PHP_CMD|" "$php_file"
        else
            # Добавляем шебанг в начало файла
            sed -i "1i#!$PHP_CMD" "$php_file"
        fi
        chmod +x "$php_file"
    fi
done

# Тестируем PaymentStatusEnum
echo "Тестирование PaymentStatusEnum..."
if $PHP_CMD -f /usr/local/mgr5/include/php/PaymentStatusEnum.php 2>/dev/null; then
    echo "✅ PaymentStatusEnum работает корректно"
else
    echo "❌ Ошибка в PaymentStatusEnum"
    php -v
    php -f /usr/local/mgr5/include/php/PaymentStatusEnum.php
    exit 1
fi

# Очистка кеша BILLmanager
echo "🔄 Очистка кеша BILLmanager..."
rm -rf /usr/local/mgr5/var/cache/* /usr/local/mgr5/var/tmp/* 2>/dev/null || true

# Настройка PHP для BILLmanager (если нужно)
if [ -f /usr/local/mgr5/etc/php.ini ]; then
    echo "Настройка PHP для BILLmanager..."
    sed -i "s|^.*php_bin.*|php_bin = $PHP_CMD|" /usr/local/mgr5/etc/php.ini
fi

# Перезапускаем BILLmanager правильной командой
echo "🔄 Перезапуск BILLmanager..."
/usr/local/mgr5/sbin/mgrctl exit -m billmgr 2>/dev/null || true

# Ждем запуска BILLmanager
echo "⏳ Ожидание запуска BILLmanager (15 секунд)..."
sleep 15

# Проверяем, что BILLmanager запустился
echo "🔍 Проверка работы BILLmanager..."
if pgrep -f "bin/core billmgr" > /dev/null; then
    echo "✅ BILLmanager успешно запущен"
else
    echo "⚠️  BILLmanager может еще запускаться, проверьте через несколько секунд"
fi

echo ""
echo "✅ Установка завершена!"
echo ""
echo "Установленная версия PHP: $(php -r 'echo PHP_VERSION;')"
echo "PHP установлен в: $(which php)"
echo ""
echo "Теперь вы можете:"
echo "1. Зайти в административную панель BILLmanager"
echo "2. Обновить страницу (Ctrl+F5) для очистки кеша браузера"
echo "3. Перейти в раздел 'Методы оплаты'"
echo "4. Добавить новый метод оплаты 'Paynet'"
echo "5. Настроить параметры модуля:"
echo "   • Код мерчанта"
echo "   • Секретный ключ"
echo "   • Пользователь API"
echo "   • Пароль API"
echo "   • Тестовый режим (опционально)"
echo ""
echo "Логи модуля записываются в /usr/local/mgr5/var/pmpaynet.log"
